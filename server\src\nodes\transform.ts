import { NodeRun, TransformNodeData } from '../../types/workflow';
import { validateSchema } from '../utils/schema';
import { VM } from 'vm2';
import Handlebars from 'handlebars';
import { get } from 'lodash';

export class TransformNodeHandler {
  private node: TransformNodeData;

  constructor(node: TransformNodeData) {
    this.node = node;
  }

  private log(level: string, ...args: any[]) {
    // Implement logging logic here
    console.log(`[Transform Node ${this.node.id}] ${level}:`, ...args);
  }

  private validateInput(input: any): boolean {
    if (!this.node.data.validation?.enabled) {
      return true;
    }

    try {
      // Use the validation schema if provided, otherwise fall back to inputSchema
      const schema = this.node.data.validation?.schema || this.node.data.inputSchema;
      return validateSchema(input, schema);
    } catch (error) {
      this.log('error', 'Input validation failed:', error);
      return false;
    }
  }

  private validateOutput(output: any): boolean {
    try {
      return validateSchema(output, this.node.data.outputSchema);
    } catch (error) {
      this.log('error', 'Output validation failed:', error);
      return false;
    }
  }

  private executeJavaScriptTransform(input: any): any {
    if (!this.node.data.transformation.code) {
      throw new Error('No transformation code provided');
    }

    try {
      // Create a new VM instance with the input in the sandbox
      const vm = new VM({
        timeout: this.node.data.executionSettings.timeout || 5000,
        sandbox: {
          input,
          console: {
            log: (...args: any[]) => this.log('info', ...args),
            error: (...args: any[]) => this.log('error', ...args),
            warn: (...args: any[]) => this.log('warn', ...args),
          },
          // Add common utilities
          JSON,
          Math,
          Date,
          Array,
          Object,
          String,
          Number,
          Boolean,
        },
      });

      // Wrap the user code to ensure it returns a value
      const code = `
        ${this.node.data.transformation.code}

        // If transform function exists, call it with input
        if (typeof transform === 'function') {
          transform(input);
        } else {
          // If no transform function, the code should return a value directly
          input;
        }
      `;

      return vm.run(code);
    } catch (error) {
      this.log('error', 'JavaScript transformation failed:', error);
      throw error;
    }
  }

  private executeTemplateTransform(input: any): any {
    if (!this.node.data.transformation.template) {
      throw new Error('No transformation template provided');
    }

    try {
      // Register common Handlebars helpers
      Handlebars.registerHelper('json', function(context) {
        return JSON.stringify(context);
      });

      Handlebars.registerHelper('eq', function(a, b) {
        return a === b;
      });

      Handlebars.registerHelper('ne', function(a, b) {
        return a !== b;
      });

      const template = Handlebars.compile(this.node.data.transformation.template);
      const result = template(input);

      // Try to parse as JSON if it looks like JSON, otherwise return as string
      if (typeof result === 'string' && (result.startsWith('{') || result.startsWith('['))) {
        try {
          return JSON.parse(result);
        } catch {
          // If JSON parsing fails, return as string
          return result;
        }
      }

      return result;
    } catch (error) {
      this.log('error', 'Template transformation failed:', error);
      throw new Error(`Template compilation/execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private executeMappingTransform(input: any): any {
    if (!this.node.data.transformation.mapping) {
      throw new Error('No transformation mapping provided');
    }

    const output: Record<string, any> = {};

    for (const [outputKey, inputPath] of Object.entries(this.node.data.transformation.mapping)) {
      try {
        // Ensure inputPath is a string for lodash get
        const path = String(inputPath);
        output[outputKey] = get(input, path);
      } catch (error) {
        this.log('warn', `Failed to map ${outputKey} from path ${inputPath}:`, error);
        output[outputKey] = null;
      }
    }

    return output;
  }

  public async execute(input: any): Promise<NodeRun> {
    const startTime = new Date();
    let output: any = null;
    let error: string | null = null;

    try {
      // Validate input
      if (!this.validateInput(input)) {
        throw new Error(this.node.data.validation?.errorMessage || 'Input validation failed');
      }

      // Execute transformation based on type
      switch (this.node.data.transformation.type) {
        case 'javascript':
          output = this.executeJavaScriptTransform(input);
          break;
        case 'template':
          output = this.executeTemplateTransform(input);
          break;
        case 'mapping':
          output = this.executeMappingTransform(input);
          break;
        default:
          throw new Error(`Unsupported transformation type: ${this.node.data.transformation.type}`);
      }

      // Validate output if validation is enabled and output schema is defined
      if (this.node.data.outputSchema && !this.validateOutput(output)) {
        throw new Error('Output validation failed');
      }

      this.log('info', `Transform completed successfully. Input type: ${typeof input}, Output type: ${typeof output}`);

    } catch (err) {
      error = err instanceof Error ? err.message : 'Unknown error occurred';
      this.log('error', error);
      // Set output to null on error
      output = null;
    }

    const endTime = new Date();
    const executionDuration = endTime.getTime() - startTime.getTime();

    return {
      id: 0, // This will be set by the storage layer
      workflowRunId: 0, // This will be set by the workflow executor
      nodeId: this.node.id,
      nodeType: 'transform',
      nodeName: this.node.data.name || `Transform Node ${this.node.id}`,
      status: error ? 'failed' : 'completed',
      startTime,
      endTime: endTime,
      executionDuration,
      input,
      output,
      error,
    };
  }
}