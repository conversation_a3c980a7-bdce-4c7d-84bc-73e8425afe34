import { NodeRun, TransformNodeData } from '../../types/workflow';
import { validateSchema } from '../utils/schema';
import { VM } from 'vm2';
import Handlebars from 'handlebars';
import { get } from 'lodash';

export class TransformNodeHandler {
  private node: TransformNodeData;
  private vm: VM;

  constructor(node: TransformNodeData) {
    this.node = node;
    this.vm = new VM({
      timeout: node.data.executionSettings.timeout || 5000,
      sandbox: {
        console: {
          log: (...args: any[]) => this.log('info', ...args),
          error: (...args: any[]) => this.log('error', ...args),
          warn: (...args: any[]) => this.log('warn', ...args),
        },
        // Add more sandboxed globals as needed
      },
    });
  }

  private log(level: string, ...args: any[]) {
    // Implement logging logic here
    console.log(`[Transform Node ${this.node.id}] ${level}:`, ...args);
  }

  private validateInput(input: any): boolean {
    if (!this.node.data.validation?.enabled) {
      return true;
    }

    try {
      return validateSchema(input, this.node.data.inputSchema);
    } catch (error) {
      this.log('error', 'Input validation failed:', error);
      return false;
    }
  }

  private validateOutput(output: any): boolean {
    try {
      return validateSchema(output, this.node.data.outputSchema);
    } catch (error) {
      this.log('error', 'Output validation failed:', error);
      return false;
    }
  }

  private executeJavaScriptTransform(input: any): any {
    if (!this.node.data.transformation.code) {
      throw new Error('No transformation code provided');
    }

    const code = `
      ${this.node.data.transformation.code}
      transform(input);
    `;

    try {
      return this.vm.run(code, { input });
    } catch (error) {
      this.log('error', 'JavaScript transformation failed:', error);
      throw error;
    }
  }

  private executeTemplateTransform(input: any): any {
    if (!this.node.data.transformation.template) {
      throw new Error('No transformation template provided');
    }

    try {
      const template = Handlebars.compile(this.node.data.transformation.template);
      return template(input);
    } catch (error) {
      this.log('error', 'Template transformation failed:', error);
      throw error;
    }
  }

  private executeMappingTransform(input: any): any {
    if (!this.node.data.transformation.mapping) {
      throw new Error('No transformation mapping provided');
    }

    const output: Record<string, any> = {};

    for (const [outputKey, inputPath] of Object.entries(this.node.data.transformation.mapping)) {
      output[outputKey] = get(input, inputPath);
    }

    return output;
  }

  public async execute(input: any): Promise<NodeRun> {
    const startTime = new Date();
    let output: any;
    let error: string | null = null;

    try {
      // Validate input
      if (!this.validateInput(input)) {
        throw new Error(this.node.data.validation?.errorMessage || 'Input validation failed');
      }

      // Execute transformation based on type
      switch (this.node.data.transformation.type) {
        case 'javascript':
          output = this.executeJavaScriptTransform(input);
          break;
        case 'template':
          output = this.executeTemplateTransform(input);
          break;
        case 'mapping':
          output = this.executeMappingTransform(input);
          break;
        default:
          throw new Error(`Unsupported transformation type: ${this.node.data.transformation.type}`);
      }

      // Validate output
      if (!this.validateOutput(output)) {
        throw new Error('Output validation failed');
      }

    } catch (err) {
      error = err instanceof Error ? err.message : 'Unknown error occurred';
      this.log('error', error);
    }

    const endTime = new Date();
    const executionDuration = endTime.getTime() - startTime.getTime();

    return {
      id: 0, // This will be set by the storage layer
      workflowRunId: 0, // This will be set by the workflow executor
      nodeId: this.node.id,
      nodeType: 'transform',
      nodeName: this.node.data.name,
      status: error ? 'failed' : 'completed',
      startTime,
      endTime,
      executionDuration,
      input,
      output,
      error,
    };
  }
} 