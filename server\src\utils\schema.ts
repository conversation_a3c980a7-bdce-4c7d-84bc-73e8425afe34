import { JSDataType } from '../../types/workflow';

export function validateSchema(data: any, schema: any): boolean {
  if (!schema || !data) {
    return false;
  }

  // Check if the data type matches the schema type
  if (schema.type) {
    if (!validateType(data, schema.type)) {
      return false;
    }
  }

  // Validate properties if schema has them
  if (schema.properties) {
    for (const [key, propSchema] of Object.entries(schema.properties)) {
      const propValue = data[key];
      const propType = (propSchema as any).type;

      // Check required properties
      if ((propSchema as any).required && propValue === undefined) {
        return false;
      }

      // Skip validation for undefined optional properties
      if (propValue === undefined) {
        continue;
      }

      // Validate property type
      if (!validateType(propValue, propType)) {
        return false;
      }

      // Recursively validate nested objects
      if (propType === 'object' && (propSchema as any).properties) {
        if (!validateSchema(propValue, propSchema)) {
          return false;
        }
      }

      // Validate array items
      if (propType === 'array' && (propSchema as any).items) {
        if (!Array.isArray(propValue)) {
          return false;
        }
        for (const item of propValue) {
          if (!validateSchema(item, (propSchema as any).items)) {
            return false;
          }
        }
      }
    }
  }

  return true;
}

function validateType(value: any, type: JSDataType): boolean {
  switch (type) {
    case 'string':
      return typeof value === 'string';
    case 'number':
      return typeof value === 'number' && !isNaN(value);
    case 'boolean':
      return typeof value === 'boolean';
    case 'object':
      return typeof value === 'object' && value !== null && !Array.isArray(value);
    case 'array':
      return Array.isArray(value);
    case 'date':
      return value instanceof Date || !isNaN(Date.parse(value));
    case 'file':
      return value instanceof File || value instanceof Blob;
    case 'any':
      return true;
    default:
      return false;
  }
} 